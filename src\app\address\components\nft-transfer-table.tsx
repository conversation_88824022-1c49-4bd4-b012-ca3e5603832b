"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { CSVExportButton } from "@/components/csv-button-export"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, formatTimestamp, normalizeAddress } from "@/helpers/format"
import CopyTooltip from "@/app/components/copy-tooltip"
import type { DaumNftTransfer } from "@/types/nft"
import { NFTImage, TokenLogo } from "@/components/nft-image"

interface NFTTransfersTableProps {
  transfers: DaumNftTransfer[]
  loadingFallback?: React.ReactNode
  showExport?: boolean
  exportFilename?: string
}

export function NFTTransfersTable({
  transfers,
  loadingFallback,
  showExport = false,
  exportFilename = "nft-transfers.csv",
}: NFTTransfersTableProps) {
  const router = useRouter()

  // Format data for CSV export
  const formatNftTransfersForCSV = (transfers: DaumNftTransfer[]) => {
    return transfers.map((transfer) => ({
      "Transaction Hash": transfer.transactionHash,
      Method: transfer.functionSignature || "Transfer",
      Block: transfer.blockNumber,
      "Date(UTC)": new Date(transfer.timestamp).toISOString().split("T")[0],
      UnixTimeStamp: Math.floor(new Date(transfer.timestamp).getTime() / 1000),
      Age: formatTimestamp(transfer.timestamp),
      From: normalizeAddress(transfer.from),
      To: normalizeAddress(transfer.to),
      "Token ID": transfer.tokenId,
      "Token Address": normalizeAddress(transfer.tokenAddress),
      "Token Name": transfer.tokenDetails?.name || "Unknown",
      "Token Symbol": transfer.tokenDetails?.symbol || "Unknown",
      "NFT Name": transfer.nftDetails?.standard || "Unknown",
      Amount: transfer.amount,
      Value: transfer.value,
    }))
  }

  const handleNftClick = (transfer: DaumNftTransfer) => {
    if (transfer.tokenAddress && transfer.tokenId) {
      router.push(`/nft/${transfer.tokenAddress}/${transfer.tokenId}`)
    }
  }

  const renderTokenLogo = (tokenDetails: DaumNftTransfer["tokenDetails"]) => {
    return (
      <TokenLogo
        logoHash={tokenDetails?.logo}
        tokenName={tokenDetails?.name}
        size="sm"
      />
    )
  }

  const renderNftImage = (nftDetails: DaumNftTransfer["nftDetails"]) => {
    return (
      <NFTImage
        src={nftDetails?.image}
        alt={`NFT #${nftDetails?.tokenId || "Unknown"}`}
        size="sm"
        rounded={false}
      />
    )
  }

  if (loadingFallback) {
    return <>{loadingFallback}</>
  }

  return (
    <div>
      {showExport && transfers.length > 0 && (
        <div className="flex justify-end mb-4">
          <CSVExportButton
            data={transfers}
            formatter={formatNftTransfersForCSV}
            filename={exportFilename}
          />
        </div>
      )}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction Hash</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Block</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>From</TableHead>
              <TableHead>To</TableHead>
              <TableHead>Token ID</TableHead>
              <TableHead>NFT</TableHead>
              <TableHead>Collection</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transfers.map((transfer, index) => (
              <TableRow key={`${transfer.transactionHash}-${index}`}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <CopyTooltip content={transfer.transactionHash} />
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger
                          className="text-blue-700 hover:underline cursor-pointer"
                          onClick={() => {
                            router.push(
                              PATH_ROUTER.TRANSACTION_DETAIL(
                                transfer.transactionHash,
                              ),
                            )
                          }}
                        >
                          {formatHash(transfer.transactionHash, 10, 8)}
                        </TooltipTrigger>
                        <TooltipContent>
                          {transfer.transactionHash}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {transfer.functionSignature || "Transfer"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger
                        className="text-blue-700 hover:underline cursor-pointer"
                        onClick={() => {
                          router.push(
                            PATH_ROUTER.BLOCK_DETAIL(
                              transfer.blockNumber.toString(),
                            ),
                          )
                        }}
                      >
                        {transfer.blockNumber}
                      </TooltipTrigger>
                      <TooltipContent>
                        Block #{transfer.blockNumber}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        {formatTimestamp(transfer.timestamp)}
                      </TooltipTrigger>
                      <TooltipContent>
                        {new Date(transfer.timestamp).toLocaleString()}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger
                          className="text-blue-700 hover:underline cursor-pointer"
                          onClick={() => {
                            const address = normalizeAddress(transfer.from)
                            if (address) {
                              router.push(PATH_ROUTER.ADDRESS_DETAIL(address))
                            }
                          }}
                        >
                          {formatHash(normalizeAddress(transfer.from), 6, 4)}
                        </TooltipTrigger>
                        <TooltipContent>
                          {normalizeAddress(transfer.from)}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <CopyTooltip
                      content={
                        normalizeAddress(transfer.from) || transfer.from
                      }
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger
                          className="text-blue-700 hover:underline cursor-pointer"
                          onClick={() => {
                            const address = normalizeAddress(transfer.to)
                            if (address) {
                              router.push(PATH_ROUTER.ADDRESS_DETAIL(address))
                            }
                          }}
                        >
                          {formatHash(normalizeAddress(transfer.to), 6, 4)}
                        </TooltipTrigger>
                        <TooltipContent>
                          {normalizeAddress(transfer.to)}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <CopyTooltip
                      content={normalizeAddress(transfer.to) || transfer.to}
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger
                        className="text-blue-700 hover:underline cursor-pointer font-mono"
                        onClick={() => handleNftClick(transfer)}
                      >
                        #{transfer.tokenId}
                      </TooltipTrigger>
                      <TooltipContent>
                        Token ID: {transfer.tokenId}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div
                    className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
                    onClick={() => handleNftClick(transfer)}
                  >
                    {renderNftImage(transfer.nftDetails)}
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">
                        NFT #{transfer.tokenId}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {transfer.nftDetails?.standard || "Unknown"}
                      </span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {renderTokenLogo(transfer.tokenDetails)}
                    <div className="flex flex-col">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            className="text-blue-700 hover:underline text-left cursor-pointer"
                            onClick={() => {
                              if (transfer.tokenDetails?.address) {
                                router.push(
                                  PATH_ROUTER.TOKEN_DETAIL(
                                    transfer.tokenDetails.address,
                                  ),
                                )
                              }
                            }}
                          >
                            <span className="text-sm font-medium">
                              {transfer.tokenDetails?.name || "Unknown"}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            {transfer.tokenDetails?.name || "Unknown Token"}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <span className="text-xs text-muted-foreground">
                        {transfer.tokenDetails?.symbol || "Unknown"}
                      </span>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
